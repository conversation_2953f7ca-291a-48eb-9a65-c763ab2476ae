<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateTransactionsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('transactions', function (Blueprint $table) {
            $table->id();
            $table->text('subject')->nullable();
            $table->text('header_text')->nullable();
            $table->string('from_name')->nullable();
            $table->json('components')->nullable();
            $table->dateTime('sent_on')->nullable()->default(0);
            $table->date('date')->nullable();
            $table->integer('opens')->nullable();
            $table->integer('clicks')->nullable();
            $table->longText('summary')->nullable();
            $table->string('attachment_link')->nullable();
            $table->unsignedBigInteger('template_id')->nullable();
            $table->unsignedBigInteger('group_id')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('transactions');
    }
}
