<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateCampaignsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('campaigns', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->unsignedInteger('number')->nullable();
            $table->text('subject')->nullable();
            $table->text('header_text')->nullable();
            $table->string('from_name')->nullable();
            $table->json('components')->nullable();
            $table->dateTime('schedule')->nullable();
            $table->boolean('toggle_schedule')->nullable();
            $table->dateTime('sent_on')->nullable();
            $table->date('date')->nullable();
            $table->integer('opens')->nullable();
            $table->integer('clicks')->nullable();
            $table->longText('summary')->nullable();
            $table->integer('bounces')->nullable();
            $table->integer('unsubscribers')->nullable();
            $table->string('campaign_id')->nullable();
            $table->unsignedBigInteger('template_id')->nullable();
            $table->string('list_id')->nullable();
            $table->text('segments')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('campaigns');
    }
}
