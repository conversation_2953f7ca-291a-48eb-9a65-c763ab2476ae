<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
        if (
            // true || 
            env('LOG_SQL')
        ) {
            \DB::listen(function ($query) {
                \Log::channel('sql')->info($query->time . 'sec  ' . $query->sql);
            });
        }
    }

    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot()
    {
        //
    }
}
