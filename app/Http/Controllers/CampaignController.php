<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Campaign;
use Illuminate\Support\Str;

class CampaignController extends Controller
{
    private $media;

    public function show(Campaign $campaign)
    {
        if (request()->header('User-Agent') == 'CreateSend') {
            define('CS_REST_CALL_TIMEOUT', 60);
            define('CS_REST_SOCKET_TIMEOUT', 60);
        }

        $this->media = $campaign->media;

        $components = $this->getComponents($campaign->components)->toArray();
        // return ($this->getComponents($campaign->components));
        switch (env('SERVICE')) {
            case 'union':
                return view('layouts.union', compact('components', 'campaign'));
                break;

            default:
                return view('layouts.webpage', compact('components', 'campaign'));
                break;
        }
    }

    public function getComponents($components)
    {
        return collect($components)->map(function ($component) {
            return collect($component)->merge([
                'images' => $this->getImages($component['key'], $component['layout'])
            ]);
        });
    }

    public function getImages($key, $layout)
    {
        if ($layout == 'clips') {
            $allmedia = $this->media->filter(function ($image) use ($key) {
                return Str::startsWith($image->name, $key . '__');
            });
            return [
                'image' => $allmedia->map(function ($image) {
                    if ($image->collection_name == 'images') {
                        return $image->getFullUrl('components');
                    }
                })->filter()->values(),
                'clip' => $allmedia->map(function ($image) {
                    if ($image->collection_name == 'clips') {
                        return $image->getFullUrl('components');
                    }
                })->filter()->values(),
            ];
        } else if($layout == 'banner') {
            // dont use watermark for banner
            return $this->media->sortBy('order_column')->filter(function ($image) use ($key) {
                return Str::endsWith($image->collection_name,  '_' . $key);
            })->map(function ($image) {
                return [
                    'url' =>  $image->getFullUrl(),
                    'title' => optional($image->custom_properties)['image_title'],
                    'raw' => $image->getFullUrl(),
                ];
            })
                ->values();
        } else if ($layout == 'advertisement' || $layout == 'attachment-with-image') {
            // dont use watermark for banner
            return $this->media->sortBy('order_column')->filter(function ($image) use ($key) {
                return Str::endsWith($image->collection_name,  '_' . $key);
            })->map(function ($image) {
                return [
                    'url' =>  $image->getFullUrl(),
                    'title' => optional($image->custom_properties)['image_title'],
                ];
            })
                ->values();
        }
        return $this->media->sortBy('order_column')->filter(function ($image) use ($key) {
            return Str::endsWith($image->collection_name,  '_' . $key);
        })->map(function ($image) {
		$component_url = data_get($image->generated_conversions, 'components')
			?$image->getFullUrl('components') 
			:$image->getFullUrl();
            return [
		    'url' => $image->mime_type == 'image/gif'
		    || !data_get($image->generated_conversions, 'components')
		    	? $image->getFullUrl()
		    	: $component_url,
                'title' => optional($image->custom_properties)['image_title'],
		'raw' => $image->getFullUrl(),
            ];
        })
            ->values();
    }
}
