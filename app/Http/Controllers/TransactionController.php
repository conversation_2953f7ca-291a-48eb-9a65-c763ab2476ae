<?php

namespace App\Http\Controllers;

use App\Transaction;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class TransactionController extends Controller
{
    private $media;

    public function show(Transaction $transaction)
    {
        if (request()->header('User-Agent') == 'CreateSend') {
            define('CS_REST_CALL_TIMEOUT', 60);
            define('CS_REST_SOCKET_TIMEOUT', 60);
        }

        $this->media = $transaction->media;

        $components = $this->getComponents($transaction->components)->toArray();
        // return ($this->getComponents($transaction->components));
        $replace = array(
            '/<!--[^\[](.*?)[^\]]-->/s' => '',
            "/<\?php/" => '<?php ',
            "/\n([\S])/" => '$1',
            "/\r/" => '',
            "/\n+/" => "\n",
            "/\t/" => '',
            "/ +/" => ' ',
        );

        switch (env('SERVICE')) {
            case 'union':
                return view('layouts.union', [
                    'components' => $components,
                    'campaign' => $transaction,
                ]);
                break;

            default:
                // return view('layouts.webpage', [
                //     'components' => $components,
                //     'campaign' => $transaction,
                // ]);
                return preg_replace(
                    array_keys($replace),
                    array_values($replace),
                    view('layouts.webpage', [
                        'components' => $components,
                        'campaign' => $transaction,
                    ])->render()
                );
                break;
        }
    }

    public function getComponents($components)
    {
        return collect($components)->map(function ($component) {
            return collect($component)->merge([
                'images' => $this->getImages($component['key'], $component['layout'])
            ]);
        });
    }

    public function getImages($key, $layout)
    {
        if ($layout == 'clips') {
            $allmedia = $this->media->filter(function ($image) use ($key) {
                return Str::startsWith($image->name, $key . '__');
            });
            return [
                'image' => $allmedia->map(function ($image) {
                    if ($image->collection_name == 'images') {
                        return $image->getFullUrl('components');
                    }
                })->filter()->values(),
                'clip' => $allmedia->map(function ($image) {
                    if ($image->collection_name == 'clips') {
                        return $image->getFullUrl('components');
                    }
                })->filter()->values(),
            ];
        }
        return $this->media->sortBy('order_column')->filter(function ($image) use ($key) {
            // return Str::startsWith($image->name, $key . '__');
            return Str::endsWith($image->collection_name,  '_' . $key);
        })->map(function ($image) {
            return [
                'url' => $image->mime_type == 'image/gif' ? $image->getFullUrl() : $image->getFullUrl('components'),
                'title' => optional($image->custom_properties)['image_title'],
            ];
        })
            ->values();
    }
}
