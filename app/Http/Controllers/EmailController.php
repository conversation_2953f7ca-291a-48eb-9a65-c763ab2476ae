<?php

namespace App\Http\Controllers;

use CampaignMonitor;
use Illuminate\Http\Request;

class EmailController extends Controller
{
    public function sendTestEmail($campaign, $emails)
    {
        $campaign = $this->getCampaign($campaign);

        $results = CampaignMonitor::Campaigns($campaign['id'])
            ->send_preview($emails, optional($campaign['campaign']->template)->from_email ?? env('FromEmail'));

        return request()->all();
    }

    public function sendNow($campaign)
    {
        $campaign = $this->getCampaign($campaign);

        $data = ['ConfirmationEmail' => '<EMAIL>'];
        if ($campaign['campaign']->toggle_schedule && $campaign['campaign']->schedule) {
            $data['SendDate'] = $campaign['campaign']->schedule->format('Y-m-d H:i');
            $sent_on = $campaign['campaign']->schedule;
        } else {
            $data['SendDate'] = 'immediately';
            $sent_on = now();
        }

        $results = CampaignMonitor::Campaigns($campaign['id'])
            ->send($data);

        $campaign['campaign']->update(['sent_on' => $sent_on]);

        return response()
            ->json([
                'status' => 200,
                'message' => 'Campaign was successfully sent.',
            ]);
    }

    private function getCampaign($campaign)
    {
        $drafts = \CampaignMonitor::clients(env('CAMPAIGNMONITOR_CLIENT_ID'))->get_drafts();
        $scheduled = \CampaignMonitor::clients(env('CAMPAIGNMONITOR_CLIENT_ID'))->get_scheduled();
        $campaigns = collect($drafts->response)->merge(collect($scheduled->response));
        $data = $campaigns->first(function ($item) use ($campaign) {
            return $item->Name == env('SERVICE') ? env('SERVICE') . ' - ' . $campaign->id : $campaign->id;
        });

        $campaign_data = [
            'Subject' => $campaign->subject . (env('SERVICE') == 'union' ? ' #' . $campaign->id : ''),
            'Name' => env('SERVICE') ? env('SERVICE') . ' - ' . $campaign->id : $campaign->id,
            'FromName' => $campaign->from_name ?? optional($campaign->template)->from_name ?? env('FromName'),
            'FromEmail' => optional($campaign->template)->from_email ?? env('FromEmail'),
            'ReplyTo' => optional($campaign->template)->from_email ??  env('ReplyTo'),
            'HtmlUrl' => env('APP_URL') . $campaign->path,
            // 'ListIDs' => [$campaign->list_id ?? env('CAMPAIGNMONITOR_LIST_ID')],
            // 'SegmentIDs' => $campaign->segments
        ];
        if ($campaign->segments) {
            $campaign_data['SegmentIDs'] = $campaign->segments;
        } else {
            $campaign_data['ListIDs'] = [$campaign->list_id ?? env('CAMPAIGNMONITOR_LIST_ID')];
        }

        if ($data) {
            $old = CampaignMonitor::Campaigns($data->CampaignID)->delete();
            if ($old->http_status_code == 200) {
                $new = CampaignMonitor::Campaigns()->create(env('CAMPAIGNMONITOR_CLIENT_ID'), $campaign_data);
            }
        } else {
            $new = CampaignMonitor::Campaigns()->create(env('CAMPAIGNMONITOR_CLIENT_ID'), $campaign_data);
        }

        \Log::info(json_encode($new));

        if (is_string($new->response)) {
            $campaign->update(['campaign_id' => $new->response]);
        } else {
            $campaign->update(['campaign_id' => '', 'sent_on' => null]);
            abort(500, data_get($new, 'response.Message'));
        }

        return [
            'id' => $new->response,
            'campaign' => $campaign,
        ];
    }

    public function deleteCampaign($campaign)
    {
        if ($campaign->campaign_id) {
            CampaignMonitor::Campaigns($campaign->campaign_id)->delete();
        }

        $campaign->update(['sent_on' => null]);
    }

    public function summary($campaign)
    {
        if ($campaign->campaign_id) {
            $summary = CampaignMonitor::Campaigns($campaign->campaign_id)->get_summary();
            $campaign->update([
                'opens' => $summary->response->TotalOpened,
                'clicks' => $summary->response->Clicks,
            ]);
            return $campaign->fresh();
        }
    }
    public function status($campaign)
    {
        if ($campaign->campaign_id) {
            $currentPage = 1;
            $summary = CampaignMonitor::Campaigns($campaign->campaign_id)->get_summary();
            $finalPage = ceil(data_get($summary, 'response.TotalOpened') / 1000);
            $collection = collect();
            while ($currentPage <= $finalPage) {
                $collection = $collection->merge(collect(
                    data_get(CampaignMonitor::Campaigns($campaign->campaign_id)->get_opens('', $currentPage), 'response.Results')
                ));
                $currentPage++;
            }
            $users = \App\Subscriber::pluck('name', 'email')->toArray();

            $campaign->update([
                'opens' => data_get($summary, 'response.UniqueOpened'),
                'clicks' => data_get($summary, 'response.Clicks'),
                'summary' => $collection->map(function ($item) use ($users) {
                    return ['email' => $item->EmailAddress, 'date' => $item->Date,];
                })
                    ->groupBy('email')
                    ->map(function ($user, $index) use ($users) {
                        return [
                            'email' => $index,
                            'name' => array_key_exists($index, $users) ? $users[$index] : '',
                            'date' => $user->map->date->sortDesc()->first(),
                            'count' => $user->map->date->count()
                        ];
                    })->values(),
            ]);
            return $campaign->fresh();
        }
    }
}

