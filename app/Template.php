<?php

namespace App;

use <PERSON><PERSON>\MediaLibrary\HasMedia;
use Illuminate\Database\Eloquent\Model;
use Spatie\MediaLibrary\InteractsWithMedia;

class Template extends Model implements HasMedia
{
    use InteractsWithMedia;

    protected $guarded = [];

    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('header_picture')->singleFile();
        $this->addMediaCollection('footer_picture')->singleFile();
    }

    public function campaigns()
    {
        return $this->hasMany(Campaign::class);
    }

    public function getHeaderPictureAttribute()
    {
        return $this->getFirstMediaUrl('header_picture');
    }

    public function getFooterPictureAttribute()
    {
        return $this->getFirstMediaUrl('footer_picture');
    }
}
