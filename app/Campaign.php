<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

class Campaign extends Model implements HasMedia
{
    use InteractsWithMedia;

    public $registerMediaConversionsUsingModelInstance = true;
    
    protected $guarded = [];

    protected $with = ['media'];

    protected $casts = [
        'date' => 'date',
        'schedule' => 'datetime',
        'components' => 'array',
        'segments' => 'array',
        'sent_on' => 'datetime',
    ];

    public function template()
    {
        return $this->belongsTo(Template::class);
    }

    public function getPathAttribute()
    {
        return '/campaign/' . $this->id;
    }

    public function updateStatus()
    {
        (new \App\Http\Controllers\EmailController)->status($this);
    }

    public function getStatusAttribute()
    {
        if ($this->sent_on && $this->schedule && $this->schedule->isAfter(now())) {
            return 'Scheduled';
        } elseif ($this->sent_on) {
            return 'Sent';
        } else {
            return 'Draft';
        }
    }

    public function registerMediaConversions(Media $media = null): void
    {
        $this->addMediaConversion('thumb')
            ->width(130)
            ->height(130);

        if (optional($this->template)->watermark) {
            $this->addMediaConversion('components')
                ->width(1500)
                ->apply()
                ->watermark(public_path() . '/img/bechatzroseini.png')
                ->watermarkOpacity(75)
                ->watermarkPadding(10)
                ->watermarkHeight(350)
                ->apply();
        } else {
            $this->addMediaConversion('components')
                ->width(1500);
        }
    }    

    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('components');

        $this->addMediaCollection('banner');
    }

    public static function boot()
    {
        parent::boot();

        self::deleted(function ($model) {
            if ($model->campaign_id) {
                \CampaignMonitor::Campaigns($model->campaign_id)->delete();
            }
        });
    }
}
