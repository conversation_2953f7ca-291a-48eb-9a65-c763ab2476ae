<?php

namespace App\Nova;

use Illuminate\Http\Request;
use <PERSON>vel\Nova\Fields\Text;
use <PERSON>vel\Nova\Fields\Select;
use Whitecube\NovaFlexibleContent\Flexible;
use Epartment\NovaDependencyContainer\HasDependencies;
use Epartment\NovaDependencyContainer\NovaDependencyContainer;

class Segment extends Resource
{
    use HasDependencies;

    private $fields;

    public function __construct($resource)
    {
        $this->fields = \App\Field::all();
        parent::__construct($resource);
    }

    public static $model = 'App\Segment';

    public static $title = 'name';

    public static $search = [
        'id',
        'name',
    ];

    public function fields(Request $request)
    {
        return [
            Text::make('Name')
                ->sortable(),

            Flexible::make('Rules')
            ->addLayout('Rule Group', 'rules', [
                Flexible::make('Rule Detail')
                    ->addLayout('Rule Detail', 'rules', [
                        Select::make('Field')
                            ->options($this->fields->pluck('name', 'name'))
                            ->size('w-1/2'),
                        \NovaAjaxSelect\AjaxSelect::make('Value')
                            ->get('/admin-api/field/{field}/values')
                            ->parent('field')
                            ->size('w-1/2'),
                    ])->button('Add OR')
            ])
            ->onlyOnForms()
            ->button('Add AND'),
        ];
    }

    /**
     * Get the cards available for the request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function cards(Request $request)
    {
        return [];
    }

    /**
     * Get the filters available for the resource.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function filters(Request $request)
    {
        return [];
    }

    /**
     * Get the lenses available for the resource.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function lenses(Request $request)
    {
        return [];
    }

    /**
     * Get the actions available for the resource.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function actions(Request $request)
    {
        return [];
    }
}
