<?php

namespace App\Nova;

use App\Nova\Actions\UpdateFieldByEmail;
use Illuminate\Http\Request;
use <PERSON>vel\Nova\Fields\Text;
use <PERSON><PERSON>\Nova\Fields\Select;

class Subscriber extends Resource
{
    public static $model = 'App\Subscriber';

    public static $title = 'name';

    public static $search = [
        'name',
        'email',
    ];

    public function fields(Request $request)
    {
        return [
            Text::make('Name')
                ->sortable(),

            Text::make('Email')
                ->sortable()
                ->rules('required', 'email', 'max:254')
                ->creationRules('unique:subscribers,email')
                ->updateRules('unique:subscribers,email,{{resourceId}}'),

            Select::make('State')
                ->options([
                    'Subscribe' => 'Subscribe',
                    'Unsubscribed' => 'Unsubscribed',
                    'Deleted' => 'Deleted',
                ]),

            ...$this->options(),
        ];
    }

    /**
     * Get the cards available for the request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function cards(Request $request)
    {
        return [];
    }

    /**
     * Get the filters available for the resource.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function filters(Request $request)
    {
        return [];
    }

    /**
     * Get the lenses available for the resource.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function lenses(Request $request)
    {
        return [];
    }

    /**
     * Get the actions available for the resource.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function actions(Request $request)
    {
        return [
            (new UpdateFieldByEmail),
        ];
    }
    private function options()
    {
        return \App\Field::all()->map(function ($field) {
            $options = collect(explode(',', $field->values))
                ->map(function ($item) {
                    return [
                        'label' => trim($item),
                        'value' => trim($item)
                    ];
                })->filter();
            return \Laravel\Nova\Fields\Select::make(camelToTitle($field->name), $field->name)
                ->nullable()
                ->options($options)
                ->searchable();
        });
    }
}
