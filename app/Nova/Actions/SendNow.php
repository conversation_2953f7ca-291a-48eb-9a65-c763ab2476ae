<?php

namespace App\Nova\Actions;

use Illuminate\Bus\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Collection;
use Laravel\Nova\Actions\Action;
use Laravel\Nova\Fields\ActionFields;

class SendNow extends Action
{
    use InteractsWithQueue, Queueable;

    public $onlyOnDetail = true;
    public $confirmButtonText = 'Send Now';
    public $confirmText = 'Are you sure you want to send now?';

    public function handle(ActionFields $fields, Collection $models)
    {
        foreach ($models as $model) {
            (new \App\Http\Controllers\EmailController)
                ->sendNow($model);
        }
    }

    /**
     * Get the fields available on the action.
     *
     * @return array
     */
    public function fields()
    {
        return [];
    }
}
