<?php

namespace App\Nova\Actions;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Collection;
use Laravel\Nova\Actions\Action;
use <PERSON>vel\Nova\Fields\ActionFields;
use <PERSON>vel\Nova\Fields\Textarea;

class AddMembers extends Action
{
    use InteractsWithQueue, Queueable;

    /**
     * Perform the action on the given models.
     *
     * @param  \Laravel\Nova\Fields\ActionFields  $fields
     * @param  \Illuminate\Support\Collection  $models
     * @return mixed
     */
    public function handle(ActionFields $fields, Collection $models)
    {
        $news = collect(preg_split('/\r\n|[\r\n]/', $fields->emails))
            ->mapWithKeys(function ($member) {
                $array = explode(',', $member);
                return [
                    trim($array[0]) => trim($array[1]),
                ];
            });

        $models->first()->update([
            'members' => collect($models->first()->members)
                ->merge($news)
                ->unique()
        ]);

        return;
    }

    /**
     * Get the fields available on the action.
     *
     * @return array
     */
    public function fields()
    {
        return [
            Textarea::make('Emails')->placeholder(
                \App\User::take(3)
                    ->pluck('email', 'name')
                    ->map(function ($email, $name) {
                        return $name . ', ' . $email;
                    })
                    ->join('
')
            ),
        ];
    }
}
