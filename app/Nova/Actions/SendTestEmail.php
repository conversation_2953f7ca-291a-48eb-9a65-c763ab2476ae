<?php

namespace App\Nova\Actions;

use Illuminate\Bus\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Collection;
use Laravel\Nova\Actions\Action;
use <PERSON>vel\Nova\Fields\ActionFields;
use <PERSON>vel\Nova\Fields\Text;
use <PERSON>vel\Nova\Fields\BooleanGroup;

class SendTestEmail extends Action
{
    use InteractsWithQueue, Queueable;

    public $onlyOnDetail = true;
    public $confirmButtonText = 'Send Test';

    public function handle(ActionFields $fields, Collection $models)
    {
        $emails = array_filter(
            array_merge(
                collect($fields->emails)->filter()->keys()->toArray(),
                explode(',', $fields->additional)
            )
        );

        foreach ($models as $model) {
            (new \App\Http\Controllers\EmailController)
                ->sendTestEmail($model, $emails);
        }
    }


    public function fields()
    {
        return [
            BooleanGroup::make('Emails')
                ->withMeta([
                    'value' => \App\User::all()->pluck('email')->mapWithKeys(function ($item) {
                        return [$item => true];
                    })
                ])
                ->options(\App\User::all()->pluck('email', 'email')->toArray()),
            Text::make('Additional Emails', 'additional')
                ->help('Comma separated list. ex: ' . \App\User::take(3)->pluck('email')->join(',')),
        ];
    }
}
