<?php

namespace App\Nova\Actions;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Collection;
use <PERSON>vel\Nova\Actions\DestructiveAction;
use <PERSON><PERSON>\Nova\Fields\ActionFields;

class MarkAsNotSent extends DestructiveAction
{
    use InteractsWithQueue, Queueable;

    public $confirmText = 'I hope you know what you are doing!!';
    public $confirmButtonText = 'I know What I\'m doing.';
    /**
     * Perform the action on the given models.
     *
     * @param  \Laravel\Nova\Fields\ActionFields  $fields
     * @param  \Illuminate\Support\Collection  $models
     * @return mixed
     */
    public function handle(ActionFields $fields, Collection $models)
    {
        $models->each->update([
            'schedule'=> null,
            'toggle_schedule'=> null,
            'sent'=> null,
            'sent_on'=> null,
        ]);
    }

    /**
     * Get the fields available on the action.
     *
     * @return array
     */
    public function fields()
    {
        return [];
    }
}
