<?php

namespace App\Nova\Actions;

use Illuminate\Bus\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Collection;
use <PERSON>vel\Nova\Actions\Action;
use Laravel\Nova\Fields\ActionFields;

class DuplicateCampaign extends Action
{
    use InteractsWithQueue, Queueable;

    public $confirmText = 'It may take a minute to duplicate all images, please make sure all images are copyed before saving the new campaign.';

    public $confirmButtonText = 'Duplicate';

    public $onlyOnDetail = true;
    /**
     * Perform the action on the given models.
     *
     * @param  \Laravel\Nova\Fields\ActionFields  $fields
     * @param  \Illuminate\Support\Collection  $models
     * @return mixed
     */
    public function handle(ActionFields $fields, Collection $models)
    {
        $capmaign = $models->first();
        $newModel = $capmaign->replicate();

        $newModel->sent = false;
        $newModel->opens = null;
        $newModel->clicks = null;
        $newModel->summary = null;
        $newModel->schedule = null;
        $newModel->toggle_schedule = false;
        $newModel->campaign_id = null;

        $newModel->push();

        $capmaign->media->each(function ($media) use ($capmaign, $newModel) {
            dispatch(function () use ($capmaign, $newModel, $media) {
                $media->copy($newModel, $media->collection_name);
            });
        });

        return Action::push("/resources/campaigns");
    }

    /**
     * Get the fields available on the action.
     *
     * @return array
     */
    public function fields()
    {
        return [];
    }
}
