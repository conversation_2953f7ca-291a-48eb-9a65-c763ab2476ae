<?php

namespace App\Nova\Actions;

use Illuminate\Bus\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Collection;
use Laravel\Nova\Actions\Action;
use Laravel\Nova\Fields\ActionFields;

class DeleteSchedule extends Action
{
    use InteractsWithQueue, Queueable;

    public $onlyOnDetail = true;
    public $confirmButtonText = 'Delete Schedule';

    public function handle(ActionFields $fields, Collection $models)
    {
        foreach ($models as $model) {
            if ($model->campaign_id) {
                \CampaignMonitor::Campaigns($model->campaign_id)->delete();
            }

            $model->update(['sent' => false]);
        }
    }

    public function fields()
    {
        return [];
    }
}
