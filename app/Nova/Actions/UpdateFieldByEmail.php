<?php

namespace App\Nova\Actions;

use Illuminate\Bus\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Collection;
use CampaignMonitor;
use <PERSON>vel\Nova\Actions\Action;
use Laravel\Nova\Fields\ActionFields;
use <PERSON><PERSON>\Nova\Fields\Select;
use <PERSON>vel\Nova\Fields\Textarea;

class UpdateFieldByEmail extends Action
{
    use InteractsWithQueue, Queueable;

    public $standalone = true;
    /**
     * Perform the action on the given models.
     *
     * @param  \Laravel\Nova\Fields\ActionFields  $fields
     * @param  \Illuminate\Support\Collection  $models
     * @return mixed
     */
    public function handle(ActionFields $fields, Collection $models)
    {
        // collect(preg_split('/\r\n|[\r\n]|,/', $fields->emails))
        //     ->each(function ($email) use ($fields, $subscribers) {
        //         if ($subscriber = \App\Subscriber::firstWhere('email', trim($email))) {
        //             $subscribers->push($subscriber);
        //             $subscriber->update([
        //                 $fields->key => $fields->value,
        //             ]);
        //         }
        //     });
        $emails = collect(preg_split('/\r\n|[\r\n]|,/', $fields->emails))
            ->map(function ($email) {
                return trim($email);
            });

        // $subscribers = \App\Subscriber::where('email', $emails)->get();

        $emails->each(function ($email, $index) use ($fields) {
            dispatch(function () use ($email, $fields) {
                if ($subscriber = \App\Subscriber::firstWhere(['email' => $email])) {
                    $subscriber->update([
                        $fields->key => $fields->value,
                    ]);
                }
            })->delay(now()->addSeconds($index * 3));
        });
        return;
        // $subscribers->each(function ($subscriber, $index) use ($fields) {
        //     dispatch(function () use ($subscriber, $fields) {
        //         $subscriber->update([
        //             $fields->key => $fields->value,
        //         ]);
        //     })->delay(now()->addSeconds($index * 3));
        // });
        // \App\Subscriber::withoutEvents(function () use ($subscribers, $fields) {
        //     $subscribers->each->update([
        //         $fields->key => $fields->value,
        //     ]);
        // });

        // CampaignMonitor::Subscribers(env('CAMPAIGNMONITOR_LIST_ID'))
        //     ->import(
        //         $subscribers->map(function ($subscriber) use ($fields) {
        //             return [
        //                 'EmailAddress' => $subscriber->email,
        //                 'CustomFields' => [
        //                     [
        //                         'Key' => $fields->key,
        //                         'Value' => $fields->value,
        //                     ]
        //                 ],
        //             ];
        //         })->toArray(),
        //         true
        //     );
    }

    /**
     * Get the fields available on the action.
     *
     * @return array
     */
    public function fields()
    {
        return [
            Textarea::make('Emails'),

            Select::make('Key')
                ->options(\App\Field::all()->pluck('name', 'name')),

            \NovaAjaxSelect\AjaxSelect::make('Value')
                ->get('/admin-api/field/{key}/values')
                ->parent('key'),
        ];
    }
}
