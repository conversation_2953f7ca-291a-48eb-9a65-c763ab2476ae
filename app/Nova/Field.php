<?php

namespace App\Nova;

use Illuminate\Http\Request;
use <PERSON><PERSON>\Nova\Fields\Text;
use <PERSON><PERSON>\Nova\Fields\Textarea;
use <PERSON><PERSON>\Nova\Fields\Select;

class Field extends Resource
{
    public static $model = 'App\Field';

    public static $title = 'name';

    public static $search = [
        'id',
        'name',
    ];

    public function fields(Request $request)
    {
        return [
            Text::make('Name')
                ->creationRules('unique:fields,name')
                ->updateRules('unique:fields,name,{{resourceId}}')
                ->fillUsing(function ($request, $model, $attribute, $requestAttribute) {
                    $model->{$attribute} = \Str::camel($request->input($attribute));
                })
                ->resolveUsing(function ($name) {
                    return camelToTitle($name);
                })
                ->sortable(),

            Textarea::make('Values')
                ->alwaysShow()
                ->help('ex: Yes,No')
                ->withMeta(['extraAttributes' => [
                    'placeholder' => 'Comma separated list']
                ]),

            Select::make('Type')
                ->options([
                    'Text' => 'Text',
                    'Number' => 'Number',
                    'Date' => 'Date',
                ])
                ->help('You wont be able to change it once created.')
                ->readonly(!!$this->id)
        ];
    }

    /**
     * Get the cards available for the request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function cards(Request $request)
    {
        return [];
    }

    /**
     * Get the filters available for the resource.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function filters(Request $request)
    {
        return [];
    }

    /**
     * Get the lenses available for the resource.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function lenses(Request $request)
    {
        return [];
    }

    /**
     * Get the actions available for the resource.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function actions(Request $request)
    {
        return [];
    }
}
