<?php

namespace App\Nova\Flexible\Layouts;

use Fr<PERSON>la\NovaFroalaField\Froala;
use <PERSON><PERSON>\MediaLibrary\HasMedia;
use Whitecube\NovaFlexibleContent\Layouts\Layout;
use Whitecube\NovaFlexibleContent\Concerns\HasMediaLibrary;

class PlainText extends Layout implements HasMedia
{
	use HasMediaLibrary;

	/**
	 * The layout's unique identifier
	 *
	 * @var string
	 */
	protected $name = 'plain-text';

	/**
	 * The displayed title
	 *
	 * @var string
	 */
	protected $title = 'Plain Text';

	/**
	 * Get the fields displayed by the layout.
	 *
	 * @return array
	 */
	public function fields()
	{
		return [
			Froala::make('Text')->alwaysShow()
		];
	}
}
