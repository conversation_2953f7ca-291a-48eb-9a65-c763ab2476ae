<?php

namespace App\Nova\Flexible\Layouts;

use <PERSON><PERSON>\Nova\Fields\Text;
use <PERSON>haulH<PERSON>\AdvancedNovaMediaLibrary\Fields\Images;
use <PERSON><PERSON>\MediaLibrary\HasMedia;
use Whitecube\NovaFlexibleContent\Layouts\Layout;
use Whitecube\NovaFlexibleContent\Concerns\HasMediaLibrary;

class AttachmentLink extends Layout implements HasMedia
{
	use HasMediaLibrary;

	/**
	 * The layout's unique identifier
	 *
	 * @var string
	 */
	protected $name = 'attachment-link';

	/**
	 * The displayed title
	 *
	 * @var string
	 */
	protected $title = 'Attachment Link';

	/**
	 * Get the fields displayed by the layout.
	 *
	 * @return array
	 */
	public function fields()
	{
		return [
			Text::make('Link'),
		];
	}
}
