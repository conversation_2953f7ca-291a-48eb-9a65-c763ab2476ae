<?php

namespace App\Nova\Flexible\Layouts;

use Fr<PERSON>la\NovaFroalaField\Froala;
use <PERSON><PERSON>\Nova\Fields\Text;
use Ebess\AdvancedNovaMediaLibrary\Fields\Images;
use Spatie\MediaLibrary\HasMedia;
use Whitecube\NovaFlexibleContent\Layouts\Layout;
use Whitecube\NovaFlexibleContent\Concerns\HasMediaLibrary;

class Advertisement extends Layout implements HasMedia
{
	use HasMediaLibrary;

	/**
	 * The layout's unique identifier
	 *
	 * @var string
	 */
	protected $name = 'advertisement';

	/**
	 * The displayed title
	 *
	 * @var string
	 */
	protected $title = 'advertisement';

	/**
	 * Get the fields displayed by the layout.
	 *
	 * @return array
	 */
	public function fields()
	{
		return [
			Images::make('Banner')
				->withMeta([
					'uploadsToVapor' => true,
				]),
			Text::make('Link'),
			Text::make('Text')
		];
	}
}
