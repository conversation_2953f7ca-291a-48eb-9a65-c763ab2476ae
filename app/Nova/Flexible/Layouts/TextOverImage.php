<?php

namespace App\Nova\Flexible\Layouts;

use <PERSON><PERSON>\Nova\Fields\Text;
use E<PERSON>s\AdvancedNovaMediaLibrary\Fields\Images;
use Spatie\MediaLibrary\HasMedia;
use Whitecube\NovaFlexibleContent\Layouts\Layout;
use Whitecube\NovaFlexibleContent\Concerns\HasMediaLibrary;

class TextOverImage extends Layout implements HasMedia
{
	use HasMediaLibrary;

	/**
	 * The layout's unique identifier
	 *
	 * @var string
	 */
	protected $name = 'text-over-image';

	/**
	 * The displayed title
	 *
	 * @var string
	 */
	protected $title = 'Text Over Image';

	/**
	 * Get the fields displayed by the layout.
	 *
	 * @return array
	 */
	public function fields()
	{
		return [
			Images::make('Banner')
				->withMeta([
					'uploadsToVapor' => true,
				])
				->customPropertiesFields([
					Text::make('Image Title'),
				])
		];
	}
}
