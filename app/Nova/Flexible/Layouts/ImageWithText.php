<?php

namespace App\Nova\Flexible\Layouts;

use <PERSON><PERSON>\Nova\Fields\Text;
use Ebess\AdvancedNovaMediaLibrary\Fields\Images;
use Spatie\MediaLibrary\HasMedia;
use Whitecube\NovaFlexibleContent\Layouts\Layout;
use Whitecube\NovaFlexibleContent\Concerns\HasMediaLibrary;

class ImageWithText extends Layout implements HasMedia
{
	use HasMediaLibrary;

	/**
	 * The layout's unique identifier
	 *
	 * @var string
	 */
	protected $name = 'image-with-text';

	/**
	 * The displayed title
	 *
	 * @var string
	 */
	protected $title = 'Image With Text';

	/**
	 * Get the fields displayed by the layout.
	 *
	 * @return array
	 */
	public function fields()
	{
		return [
			Images::make('Banner')
				->withMeta([
					'uploadsToVapor' => true,
				]),
			Text::make('Text'),
		];
	}
}
