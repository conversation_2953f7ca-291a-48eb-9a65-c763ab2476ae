<?php

namespace App\Nova\Flexible\Layouts;

use E<PERSON>s\AdvancedNovaMediaLibrary\Fields\Images;
use Spatie\MediaLibrary\HasMedia;
use Whitecube\NovaFlexibleContent\Layouts\Layout;
use Whitecube\NovaFlexibleContent\Concerns\HasMediaLibrary;

class Banner extends Layout implements HasMedia
{
    use HasMediaLibrary;

    /**
     * The layout's unique identifier
     *
     * @var string
     */
    protected $name = 'banner';

    /**
     * The displayed title
     *
     * @var string
     */
    protected $title = 'Banner';

    /**
     * Get the fields displayed by the layout.
     *
     * @return array
     */
    public function fields()
    {
        return [
            Images::make('banner')
                ->withMeta([
                    'uploadsToVapor' => true,
                ])
        ];
    }
}
