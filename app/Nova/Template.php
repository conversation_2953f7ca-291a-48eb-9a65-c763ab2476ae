<?php

namespace App\Nova;

use <PERSON><PERSON>\Nova\Fields\ID;
use Timothy<PERSON>p\Color\Color;
use Illuminate\Http\Request;
use <PERSON><PERSON>\Nova\Fields\Boolean;
use <PERSON><PERSON>\Nova\Fields\Text;
use <PERSON><PERSON>\Nova\Fields\Markdown;
use Ebess\AdvancedNovaMediaLibrary\Fields\Images;

class Template extends Resource
{
    /**
     * The model the resource corresponds to.
     *
     * @var string
     */
    public static $model = \App\Template::class;

    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public static $title = 'name';

    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = [
        'id',
        'name',
    ];

    /**
     * Get the fields displayed by the resource.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function fields(Request $request)
    {
        return [
            ID::make()->sortable(),

            Text::make('Name')->rules('required'),

            Color::make('Email Backgroud Color', 'color')->nullable(),
            

            ...$this->admin(),
            Boolean::make('Watermark'),
            Text::make('From Email')->nullable(),
            Text::make('From Name')->nullable(),
        ];
    }

    /**
     * Get the cards available for the request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function cards(Request $request)
    {
        return [];
    }

    /**
     * Get the filters available for the resource.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function filters(Request $request)
    {
        return [];
    }

    /**
     * Get the lenses available for the resource.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function lenses(Request $request)
    {
        return [];
    }

    /**
     * Get the actions available for the resource.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function actions(Request $request)
    {
        return [];
    }

    public function admin()
    {
        if ($this->resource && $this->resource->admin) {
            return [
                Color::make('Email Footer Color', 'footer_color')->nullable(),
                Markdown::make('Header Html')->alwaysShow(),
                Markdown::make('Footer Html')->alwaysShow(),
            ];
        }
        return [
            Images::make('Header Picture'),
            Images::make('Footer Picture'),
        ];
    }
}
