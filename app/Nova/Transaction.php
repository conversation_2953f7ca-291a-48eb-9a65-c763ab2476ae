<?php

namespace App\Nova;

use Illuminate\Http\Request;
use Epartment\NovaDependencyContainer\HasDependencies;
use <PERSON><PERSON>\Nova\Fields\BelongsTo;
use <PERSON>vel\Nova\Fields\DateTime;
use <PERSON>vel\Nova\Fields\Hidden;
use <PERSON><PERSON>\Nova\Fields\ID;
use <PERSON><PERSON>\Nova\Fields\KeyValue;
use <PERSON>vel\Nova\Fields\Text;
use Laravel\Nova\Http\Requests\NovaRequest;
use Metrixinfo\Nova\Fields\Iframe\Iframe;

class Transaction extends Resource
{
    use HasDependencies;

    /**
     * The model the resource corresponds to.
     *
     * @var string
     */
    public static $model = \App\Transaction::class;

    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public static $title = 'subject';

    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = [
        'id',
        'subject',
    ];

    /**
     * Get the fields displayed by the resource.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function fields(Request $request)
    {
        return [
            ID::make(__('ID'), 'id')->sortable(),
            Text::make('Subject')
                ->rules('required', 'nullable')
                ->hideFromIndex(),
            Text::make('Subject')
                ->resolveUsing(function ($value) {
                    return \Str::limit(collect($value)->join(', '), 50);
                })
                ->onlyOnIndex(),

            DateTime::make('Sent On')
                ->onlyOnIndex(),

            BelongsTo::make('Template')->nullable(),

            BelongsTo::make('Group')->nullable(),

            ...(new Campaign($this))->components($this),

            Text::make('Attachment Link')
                ->onlyOnForms()
                ->nullable(),

            // Text::make('From Name')
            //     ->suggestions(\App\Transaction::pluck('from_name')->unique()->filter()->values())
            //     ->onlyOnForms()
            //     ->help("Leave empty for default. (" . env('FromName') . ")")
            //     ->nullable(),

            Hidden::make('Sent On')
                ->readonly(),

            Iframe::make('Preview', function () {
                return (new \App\Http\Controllers\TransactionController)
                    ->show($this->resource);
            }),
            // Media::make('banner')
            //     ->customPropertiesFields([
            //         Text::make('Image Title'),
            //     ])
            //     ->onlyOnForms()
            //     ->thumbnail('thumb')
            //     ->conversionOnView('thumb'),
        ];
    }

    /**
     * Get the cards available for the request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function cards(Request $request)
    {
        return [];
    }

    /**
     * Get the filters available for the resource.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function filters(Request $request)
    {
        return [];
    }

    /**
     * Get the lenses available for the resource.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function lenses(Request $request)
    {
        return [];
    }

    /**
     * Get the actions available for the resource.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function actions(Request $request)
    {
        return [
            (new Actions\SendTransaction),
        ];
    }
}
