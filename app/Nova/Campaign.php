<?php

namespace App\Nova;

use <PERSON><PERSON>\Nova\Fields\ID;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use <PERSON>vel\Nova\Fields\Text;
use <PERSON>vel\Nova\Fields\Trix;
use <PERSON>vel\Nova\Fields\Badge;
use <PERSON><PERSON>\Nova\Fields\Select;
use <PERSON>vel\Nova\Fields\Hidden;
use <PERSON><PERSON>\Nova\Fields\Number;
use <PERSON>vel\Nova\Fields\Boolean;
use Laravel\Nova\Fields\DateTime;
use Laravel\Nova\Fields\BelongsTo;
// use Giuga\LaravelNovaFieldIframe\Iframe;
use Metrixinfo\Nova\Fields\Iframe\Iframe;
use Whitecube\NovaFlexibleContent\Flexible;
use Ebess\AdvancedNovaMediaLibrary\Fields\Images;
// use ShaulHaf\AdvancedNovaMediaLibrary\Fields\Images;
use Ebess\AdvancedNovaMediaLibrary\Fields\Media;
use OptimistDigital\MultiselectField\Multiselect;
use Epartment\NovaDependencyContainer\HasDependencies;
use Epartment\NovaDependencyContainer\NovaDependencyContainer;
use Sha<PERSON>H<PERSON>\AdvancedNovaMediaLibrary\Fields\Images as ParentPicture;
use Froala\NovaFroalaField\Froala;
use Laravel\Nova\Fields\KeyValue;

class Campaign extends Resource
{
    use HasDependencies;

    public static $model = \App\Campaign::class;

    public static $title = 'id';

    public static $search = [
        'id',
        'subject',
    ];

    /**
     * Get the fields displayed by the resource.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function fields(Request $request)
    {
        return [
            ID::make()->sortable(),

            // Text::make('Subject')->hideFromIndex(),
            Text::make('Subject')
                ->resolveUsing(function ($value) {
                    return Str::limit(collect($value)->join(', '), 50);
                })
                ->onlyOnIndex(),


            Text::make('Subject')
                ->hideFromIndex()
                ->rules('required', 'nullable'),
            BelongsTo::make('Template')->nullable(),

            ...$this->components(),

            Boolean::make('Schedule', 'toggle_schedule')
                ->onlyOnForms(),

            NovaDependencyContainer::make([
                DateTime::make('Send On', 'schedule')
                    ->format('LLL')
                    ->rules('after:now', 'nullable'),
            ])->dependsOn('toggle_schedule', true),

            Text::make('From Name')
                ->suggestions(\App\Campaign::pluck('from_name')->unique()->filter()->values())
                ->onlyOnForms()
                ->help("Leave empty for template From Name or system From Name. (" . env('FromName') . ")")
                ->nullable(),

            DateTime::make('Sent On')
                ->readonly(),
            NovaDependencyContainer::make([
                Number::make('Opens')
                    ->withMeta(['extraAttributes' => ['readonly' => true]])
                    ->exceptOnForms(),
                Number::make('Clicks')
                    ->withMeta(['extraAttributes' => ['readonly' => true]])
                    ->exceptOnForms(),
            ])->dependsOn('sent_on', true),

            Number::make('Opens')
                ->onlyOnIndex(),
            Number::make('Clicks')
                ->onlyOnIndex(),

            Badge::make('Status')->types([
                'Draft' => 'bg-gray-500',
                'Scheduled' => 'bg-orange-400',
                'Sent' => 'bg-green-400',
            ]),

            \Shaulhaf\GoodTable\GoodTable::make('Summary')
                ->columns([
                    ['label' => 'Name', 'field' => 'name'],
                    ['label' => 'Email', 'field' => 'email'],
                    ['label' => 'Opens', 'field' => 'count', 'globalSearchDisabled' => true, 'type' => 'number'],
                    ['label' => 'Last Opened', 'field' => 'date', 'sortable' => false, 'globalSearchDisabled' => true],
                ])
                ->onlyOnDetail(),
            // \Shaulhaf\EmailSummary\EmailSummary::make('Summary')
            //     ->onlyOnDetail(),

            Iframe::make('Preview', function () {
                return (new \App\Http\Controllers\CampaignController)
                    ->show($this->resource)->render();
            }),

            Multiselect::make('Segments')
                ->onlyOnForms()
                ->saveAsJSON()
                ->options(\App\Segment::all()->pluck('name', 'segment_id')),

            Select::make('List Id')->options(
                \App\EmailList::pluck('title', 'list_id')
            )
                ->onlyOnForms()
                ->nullable(),
            Images::make('images')
                ->customPropertiesFields([
                    Text::make('Image Title'),
                ])
                ->onlyOnForms()
                ->croppingConfigs(['ratio' => 4 / 3])
                ->thumbnail('thumb')
                ->singleImageRules('max:10240')
                ->conversionOnView('thumb'),

            Media::make('banner')
                ->onlyOnForms()
            // ->croppingConfigs(['ratio' => 4 / 3])
            // ->thumbnail('thumb')
            // ->singleImageRules('max:10240')
            // ->conversionOnView('thumb') ,


        ];
    }

    /**
     * Get the cards available for the request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function cards(Request $request)
    {
        return [];
    }

    /**
     * Get the filters available for the resource.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function filters(Request $request)
    {
        return [];
    }

    /**
     * Get the lenses available for the resource.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function lenses(Request $request)
    {
        return [];
    }

    /**
     * Get the actions available for the resource.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function actions(Request $request)
    {
        return [
            (new Actions\SendTestEmail),

            (new Actions\ScheduleNow)
                ->canSee(function ($request) {
                    if (data_get($this->resource, 'id')) {
                        return $this->resource->toggle_schedule
                            && !$this->resource->sent_on;
                    }
                    return true;
                })->canRun(function ($request) {
                    return true;
                }),

            (new Actions\SendNow)->canSee(function ($request) {
                if (data_get($this->resource, 'id')) {
                    return !$this->resource->toggle_schedule
                        && !$this->resource->sent_on;
                }
                return true;
            })->canRun(function () {
                return true;
            }),

            (new Actions\DeleteSchedule)->canSee(function ($request) {
                if (data_get($this->resource, 'id')) {
                    return  $this->resource->status == 'Scheduled';
                }
                return true;
            })->canRun(function () {
                return true;
            }),

            (new Actions\UpdateCampaign)->canSee(function ($request) {
                if (data_get($this->resource, 'id')) {
                    return !!$this->sent_on;
                }
                return true;
            })->canRun(function () {
                return true;
            })->showOnTableRow(),

            (new Actions\DuplicateCampaign)
                ->canSee(function ($request) {
                    return true;
                })->canRun(function ($request) {
                    return true;
                }),
            // (new Actions\MarkAsUnsent),
            (new Actions\MarkAsNotSent),
        ];
    }

    public function components($class = null)
    {
        $class = $class ?? $this;
        switch (env('SERVICE')) {
            case 'union':
                return [
                    Flexible::make('Components')
                        // ->addComponentsToTop()
                        ->addLayout('Banner', 'banner', [
                            Images::make('banner')
                                ->singleMediaRules('max:10000') //10MB
                            ,
                        ])
                        ->addLayout('House', 'house', [
                            Select::make('Status')
                                ->withMeta(['placeholder' => 'None'])
                                ->options([
                                    'new' => 'New Listing',
                                    'backOnMarket' => 'Back On Market',
                                    'comingSoonToMarket' => 'Coming Soon To Market',
                                    'contract' => 'Under Contract',
                                    'offMarket' => 'Off Market',
                                    'activeOnMarket' => 'Active On Market',
                                    'currentlynotacceptinganymoreoffers' => 'Currently Not Accepting Anymore Offers',
                                    'priceDrop' => 'Price Drop',
                                ])->nullable(),

                            Text::make('Address'),
                            Number::make('Price')->rules('required'),
                            Froala::make('Points')->alwaysShow(),
                            // Trix::make('Points')->alwaysShow(),
                            Text::make('Link'),

                            Images::make('banner')
                                // ->onlyOnForms()
                                ->singleMediaRules('max:10000') //10MB
                                ->withmeta(['extraAttributes' => [
                                    'model' => $class->resource,
                                ]]),
                        ])
                        ->addLayout('Boxed Text', 'box-text', [
                            Froala::make('Text')->alwaysShow(),
                        ])
                        ->addLayout('Plain Text', 'plain-text', [
                            Froala::make('Text')->alwaysShow(),
                        ])
                        ->onlyOnForms()
                        ->button('Add Component'),
                ];
                break;

            default:
                return [
                    Flexible::make('Components')
                        ->addLayout(\App\Nova\Flexible\Layouts\Banner::class)
                        ->addLayout(\App\Nova\Flexible\Layouts\Grid::class)
                        ->addLayout(\App\Nova\Flexible\Layouts\ImageWithText::class)
                        ->addLayout(\App\Nova\Flexible\Layouts\TextOverImage::class)
                        ->addLayout(\App\Nova\Flexible\Layouts\PlainText::class)
                        ->addLayout(\App\Nova\Flexible\Layouts\Advertisement::class)
                        ->addLayout(\App\Nova\Flexible\Layouts\AttachmentWithImage::class)
                        ->addLayout(\App\Nova\Flexible\Layouts\AttachmentLink::class)
                        ->onlyOnForms()
                        ->button('Add Component'),
                ];
                break;
        }
    }
}
