<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use CampaignMonitor;

class Field extends Model
{
    public function getOptionsAttribute()
    {
        return collect(explode(',', $this->values))
            ->map(function ($item) {
                return trim($item);
            });
    }
    public static function boot()
    {
        parent::boot();

        self::saved(function ($field) {
            if ($field->wasRecentlyCreated) {
                CampaignMonitor::Lists(env('CAMPAIGNMONITOR_LIST_ID'))
                    ->create_custom_field([
                        'FieldName' => $field->name,
                        'DataType' => $field->type
                    ]);
            } elseif ($field->isDirty('name')) {
                CampaignMonitor::Lists(env('CAMPAIGNMONITOR_LIST_ID'))
                    ->update_custom_field('['. $field->getOriginal('name'). ']', [
                        'FieldName' => $field->name,
                    ]);
            }
        });
        self::deleted(function ($field) {
            CampaignMonitor::Lists(env('CAMPAIGNMONITOR_LIST_ID'))
                ->delete_custom_field('['. $field->getOriginal('name'). ']');
        });
    }
}
