<?php

namespace App;

use CampaignMonitor;
use Illuminate\Database\Eloquent\Model;

class Segment extends Model
{
    public $guarded = [];

    public $casts = [
        'rules' => 'array'
    ];

    public function formattedRules()
    {
        return collect($this->rules)->map(function ($rule) {
            return [
                'Rules' => collect(data_get($rule, 'attributes.rule_detail'))->map(function ($item) {
                    return [
                        'RuleType' => '[' . str_replace(' ', '', data_get($item, 'attributes.field')) . ']',
                        'Clause' => 'EQUALS ' . data_get($item, 'attributes.value'),
                    ];
                })->toArray()
            ];
        });
    }
    public static function boot()
    {
        parent::boot();

        self::saved(function ($segment) {
            if (!$segment->segment_id) {
                $response = CampaignMonitor::segments()
                    ->create(
                        env('CAMPAIGNMONITOR_LIST_ID'),
                        [
                            'Title' => $segment->name,
                            'RuleGroups' => $segment->formattedRules()
                        ]
                    );
                if ($response->was_successful()) {
                    $segment->withoutEvents(function () use ($segment, $response) {
                        $segment->update([
                            'segment_id' => data_get($response, 'response')
                        ]);
                    });
                } else {
                    abort(500, data_get($response, 'response.Message'));
                }
            } elseif ($segment->isDirty('name', 'rules')) {
                $response = CampaignMonitor::segments($segment->segment_id)
                    ->update([
                        'Title' => $segment->name,
                        'RuleGroups' => $segment->formattedRules()
                    ]);
                if (!$response->was_successful()) {
                    abort(500, data_get($response, 'response.Message'));
                }
            }
        });

        self::deleted(function ($segment) {
            CampaignMonitor::segments($segment->segment_id)->delete();
        });
    }
}
