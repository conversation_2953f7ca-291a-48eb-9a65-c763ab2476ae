<?php

namespace App\Policies;

use App\User;
use App\Template;
use Illuminate\Auth\Access\HandlesAuthorization;

class TemplatePolicy
{
    use HandlesAuthorization;
    
    public function viewAny(User $user)
    {
        return true;
    }

    public function view(User $user, Template $template)
    {
        return true;
    }

    public function create(User $user)
    {
        return true;
    }

    public function update(User $user, Template $template)
    {
        return !$template->admin;
    }

    public function delete(User $user, Template $template)
    {
        return !$template->admin;
    }
}
