<?php

namespace App;

use App\Http\Controllers\TransactionController;
use Illuminate\Database\Eloquent\Model;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

class Transaction extends Model implements HasMedia
{
    use InteractsWithMedia;

    protected $guarded = [];

    protected $with = ['media'];

    protected $casts = [
        'date' => 'date',
        'components' => 'array',
        'sent_on' => 'datetime',
    ];

    public function template()
    {
        return $this->belongsTo(Template::class);
    }

    public function group()
    {
        return $this->belongsTo(Group::class);
    }

    public function getPathAttribute()
    {
        return '/transaction/' . $this->id;
    }

    public function registerMediaConversions(Media $media = null): void
    {
        $this->addMediaConversion('thumb')
            ->width(130)
            ->height(130);

        $this->addMediaConversion('components')
            ->width(1500);
    }

    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('components');

        $this->addMediaCollection('banner');
    }

    public function sendNow()
    {
        $attachments = collect($this->components)
            ->filter(function ($component) {
                return
                    data_get($component, 'layout') == 'attachment-with-image'
                    || data_get($component, 'layout') == 'attachment-link'
                    // 
                ;
            })->map(function ($component) {
                $link = data_get($component, 'attributes.link');
                if(\Str::endsWith($link,  '.pdf')) {
                    return $link;
                }
            })
            ->filter()
            ->values();

        $ccs = collect(data_get($this->group, 'ccs'))
            ->map(function ($value, $index) {
                return ["$index $value"];
            })->flatten()->toArray();

        collect(data_get($this->group, 'members'))
            ->each(function ($value, $index) use ($attachments, $ccs) {
                $data = [
                    'From' => $this->group->from_name . ' ' . $this->group->from_email,
                    'To' => ["$index $value"],
                    'CC' => $ccs,
                    'Subject' => $this->subject,
                    'TrackOpens' => true,
                    'TrackClicks' => true,
                    'Html' => (new TransactionController)->show($this),
                    'Attachments' => $attachments,
                ];

                $subject = $this->subject;
                dispatch(function () use ($data, $subject) {
                    $data['Attachments'] = collect($data['Attachments'])
                        ->map(function ($item) use ($subject) {
                            return [
                                "Content" => base64_encode(file_get_contents($item)),
                                "Name" => $subject,
                                "Type" => 'application/pdf',
                            ];
                        })->values();
                    \CampaignMonitor::classicSend()->send($data, $subject, 'yes');
                });
            });
        $this->update(['sent_on' => now()]);
        return;
    }
}
