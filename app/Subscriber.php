<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use CampaignMonitor;

class Subscriber extends Model
{
    protected $guarded = [];
    
    protected $casts = [
        'fields' => 'array',
    ];

    public function getFieldsAttribute($fields)
    {
        $fields = json_decode($fields);

        $array = [];
        Field::all()->each(function ($item) use (&$array, $fields) {
            $array[$item->name] = data_get($fields, $item->name) ?? '';
        });
        return $array;
    }
    public function getAttribute($key)
    {
        if (Field::pluck('name')->contains($key)) {
            return data_get($this->fields, $key);
        }
        return parent::getAttribute($key);
    }

    public function setAttribute($key, $value)
    {
        if (Field::pluck('name')->contains($key)) {
            parent::setAttribute('fields->'.$key, $value);
        } else {
            parent::setAttribute($key, $value);
        }
    }

    public static function boot()
    {
        parent::boot();

        self::created(function ($model) {
            CampaignMonitor::Subscribers(env('CAMPAIGNMONITOR_LIST_ID'))
                ->add([
                    'EmailAddress' => $model->email,
                    'Name' => $model->name,
                    'ConsentToTrack' => 'Yes',
                    'CustomFields' => collect($model->fields)
                        ->map(function ($item, $index) {
                            return [
                                'Key' => $index,
                                'Value' => $item,
                            ];
                        })->values(),
                ]);
            switch ($model->state) {
                case 'Deleted':
                    CampaignMonitor::Subscribers(env('CAMPAIGNMONITOR_LIST_ID'))
                        ->delete($model->email);
                    break;
                case 'Unsubscribed':
                    CampaignMonitor::Subscribers(env('CAMPAIGNMONITOR_LIST_ID'))
                        ->unsubscribe($model->email);
                    break;
                case 'Subscribe':
                case 'Active':
                    CampaignMonitor::Subscribers(env('CAMPAIGNMONITOR_LIST_ID'))
                        ->update($model->email, ['Resubscribe' => true]);
                    break;
                
            }
        });

        self::updated(function ($model) {
            CampaignMonitor::Subscribers(env('CAMPAIGNMONITOR_LIST_ID'))
                ->update($model->email, [
                    'Name' => $model->name,
                    'ConsentToTrack' => 'Yes',
                    'CustomFields' => collect($model->fields)
                        ->map(function ($item, $index) {
                            return [
                                'Key' => $index,
                                'Value' => $item,
                            ];
                        })->values(),
                ]);
            switch ($model->state) {
                case 'Deleted':
                    CampaignMonitor::Subscribers(env('CAMPAIGNMONITOR_LIST_ID'))
                        ->delete($model->email);
                    break;
                case 'Unsubscribed':
                    CampaignMonitor::Subscribers(env('CAMPAIGNMONITOR_LIST_ID'))
                        ->unsubscribe($model->email);
                    break;
                case 'Subscribe':
                case 'Active':
                    CampaignMonitor::Subscribers(env('CAMPAIGNMONITOR_LIST_ID'))
                        ->update($model->email, ['Resubscribe' => true]);
                    break;
                
            }
        });
    }
}
