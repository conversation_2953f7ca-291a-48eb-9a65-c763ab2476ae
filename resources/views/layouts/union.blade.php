<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office">
	<head>
		<!--[if gte mso 9]>
			<xml>
				<o:OfficeDocumentSettings>
					<o:AllowPNG/>
					<o:PixelsPerInch>96</o:PixelsPerInch>
				</o:OfficeDocumentSettings>
			</xml>
		<![endif]-->
		<title></title>

		<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0" />
		<style type="text/css">
			a[x-apple-data-detectors]{color: inherit !important; text-decoration: none !important;}
			a[href^="tel"]:hover{text-decoration: none !important;}
			table td{mso-line-height-rule: exactly;}
			a img{border: none;}
			b, strong{font-weight:700;}
			p{margin:0;}
			th{padding: 0;}
			td{text-decoration: none;}
			a{
				outline: none;
				color: #fa5151;
				text-decoration: underline;
			}
			.highlight-phone{
				color:inherit; 
				border:none;
			}
			.nl span,
			.nl a{
				color:inherit !important; 
				text-decoration:none !important; 
				border:none !important;
			}
			a:hover{text-decoration: none !important;}
			.h-u a{text-decoration: none;}
			.h-u a:hover{text-decoration: underline !important;}
			.btn-01:hover{background-color: #e8e7e6 !important;}
			.active a:hover{opacity: 0.8;}
			.btn-01,
			.active a{transition: all 0.3s ease;}
			ul{Margin: 0 0 0 20px; padding:0;}
			img + div {
				display:none !important;
				width:0px !important;
				height:0px !important;
				opacity:0 !important;
			}
			@media only screen and (max-width:375px) and (min-width:374px){
				.gmail-fix{min-width:374px !important;}
			}
			@media only screen and (max-width:414px) and (min-width:413px){
				.gmail-fix{min-width:413px !important;}
			}
			@media only screen and (max-width:500px){
				/* default style */
				.flexible{width: 100% !important;}
				.img-flex img{width: 100% !important; height: auto !important;}
				.table-holder{display: table !important; width: 100% !important;}
				.thead{display: table-header-group !important; width: 100% !important;}
				.tfoot{display: table-footer-group !important; width: 100% !important;}
				.tflex{display: block !important; width: 100% !important;}
				.hide{display: none !important; width: 0 !important; height: 0 !important; padding: 0 !important; font-size: 0 !important; line-height: 0 !important;}
				
				.tc{margin: 0 auto !important; float: none !important;}
				.ac{text-align: center !important;}
				.h-0{height: 0 !important;}
				
				.p-0{padding: 0 !important;}
				.p-20{padding: 20px !important;}
				.p-30{padding: 30px !important;}

				.plr-0{padding-left: 0 !important; padding-right: 0 !important;}
				.plr-5{padding-left: 5px !important; padding-right: 5px !important;}
				.plr-10{padding-left: 10px !important; padding-right: 10px !important;}
				.plr-15{padding-left: 15px !important; padding-right: 15px !important;}
				.plr-20{padding-left: 20px !important; padding-right: 20px !important;}

				.pt-20{padding-top: 20px !important;}
				.pt-30{padding-top: 30px !important;}

				.pb-10{padding-bottom: 10px !important;}
				.pb-15{padding-bottom: 15px !important;}
				.pb-20{padding-bottom: 20px !important;}
				.pb-30{padding-bottom: 30px !important;}
				/* custom style */
				.fs-13{font-size: 13px !important; line-height: 21px !important;}
				.fs-14{font-size: 14px !important; line-height: 21px !important;}
				.fs-16{font-size: 16px !important; line-height: 24px !important;}
				.fs-20{font-size: 20px !important; line-height: 28px !important;}
				.fs-25{font-size: 25px !important; line-height: 34px !important;}
				.w-i185 img{width: 185px !important; height: auto !important;}
				
			}
		</style>
	</head>
	<body bgcolor="{{data_get($campaign, 'template.color')}}" style="margin:0; padding:0; -webkit-text-size-adjust:100%; -ms-text-size-adjust:100%;">
	
		<table class="gmail-fix" bgcolor="{{data_get($campaign, 'template.color')}}" width="100%" style="min-width:320px;" cellspacing="0" cellpadding="0">
			<tr>
				<td>
					<table width="100%" cellpadding="0" cellspacing="0">
						<tr>
							<td style="display:none; font-size:0; line-height:0; width:0; height:0; padding:0;">
								
							</td>
						</tr>
					</table>
				</td>
			</tr>
			<tr>
				<td style="padding: 0 10px;">
					<table class="flexible" width="600" align="center" style="margin:0 auto;" cellpadding="0" cellspacing="0">

					@if($campaign->template)
						@if($campaign->template->admin)
							<?php eval('?>' . \Blade::compileString($campaign->template->header_html, compact('campaign')))?>
						@else
						<tr>
							<td colspan="2">
								<table style="width: 100%;border-collapse: collapse;">
									<tr>
										<td colspan="2">
											<img src="{{$campaign->template->header_picture}}" style="width:100%;">
										</td>
									</tr>
								</table>
							</td>
						</tr>
						<tr class="spacer">
							<td colspan="2" style="padding: 15px;"></td>
						</tr>
						@endif
					@endif

					@foreach($components as $component)
						@include('components.' . $component['layout'], ['component' => $component])
					@endforeach



						<!-- <tr>
							<td class="pb-15" style="padding: 0 0 30px;">
								<table width="100%" cellpadding="0" cellspacing="0">
									<tr>
										<td class="img-flex" style="border-radius: 13px; -webkit-box-shadow: 0px 3px 22px 5px #DBDBDB; box-shadow: 0px 3px 22px 5px #DBDBDB;"><img src="bann-013.png" style="vertical-align:top; height: auto; width: 600px; border-radius: 13px;" border="0" width="600" alt="" /></td>
									</tr>
								</table>
							</td>
						</tr> -->


					</table>
				</td>
			</tr>
		</table>

		<!-- footer -->
		<table class="gmail-fix" bgcolor="#16405C" width="100%" style="min-width:320px;" cellspacing="0" cellpadding="0">
			<tr>
				<td>
					<table width="100%" cellpadding="0" cellspacing="0">
						<tr>
							<td style="display:none; font-size:0; line-height:0; width:0; height:0; padding:0;">
								
							</td>
						</tr>
					</table>
				</td>
			</tr>
			@if($campaign->template)
				@if($campaign->template->admin)
					<?php eval('?>' . \Blade::compileString($campaign->template->footer_html, compact('campaign')))?>
				@else
				<tr>
					<td colspan="2">
						<table style="width: 100%;border-collapse: collapse;">
							<tr>
								<td colspan="2">
									<img src="{{$campaign->template->footer_picture}}" style="width:100%;">
								</td>
							</tr>
						</table>
					</td>
				</tr>
				<tr class="spacer">
					<td colspan="2" style="padding: 15px;"></td>
				</tr>
				@endif
			@endif
		</table>
	</body>
</html>