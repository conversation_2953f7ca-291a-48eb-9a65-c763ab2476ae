<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">

<head>
	<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
	<title>Bobov 45 Email Template</title>
	<meta name="viewport" content="width=device-width, initial-scale=1.0" />
	<!-- <link rel="stylesheet" href="https://use.typekit.net/vex4wep.css"> -->
	<style type="text/css">
		@font-face {
			font-family: "heb_font";
			src: url("/fonts/IBMPlexSansHebrew-Regular.woff2") format("woff2");
			font-style: normal;
			font-weight: 400;
		}

		@font-face {
			font-family: "heb_font";
			src: url("/fonts/IBMPlexSansHebrew-Medium.woff2") format("woff2");
			font-style: normal;
			font-weight: 500;
		}

		@font-face {
			font-family: "heb_font";
			src: url("/fonts/IBMPlexSansHebrew-SemiBold.woff2") format("woff2");
			font-style: normal;
			font-weight: 600;
		}

		@font-face {
			font-family: "heb_font";
			src: url("/fonts/IBMPlexSansHebrew-Bold.woff2") format("woff2");
			font-style: normal;
			font-weight: 700;
		}

		table {
			border-collapse: separate;
		}

		a,
		a:link,
		a:visited {
			text-decoration: none;
			color: #00788a;
		}

		a:hover {
			text-decoration: underline;
		}

		h2,
		h2 a,
		h2 a:visited,
		h3,
		h3 a,
		h3 a:visited,
		h4,
		h5,
		h6,
		.t_cht {
			color: #000 !important;
		}

		.ExternalClass p,
		.ExternalClass span,
		.ExternalClass font,
		.ExternalClass td {
			line-height: 100%;
		}

		.ExternalClass {
			width: 100%;
		}

		@media screen and (max-width: 599px) {
			.jdn_logo_top {
				width: 160px !important;
			}

			.header_text {
				width: 79px;
			}

			.image_full {
				width: 145px;
			}

			.top_text {
				font-size: 18px !important;
			}

			.date_text {
				font-size: 16px !important;
			}

			.text_head {
				font-size: 14px !important;
			}

			.text_sub_head {
				font-size: 12px !important;
			}

			.spacer td {
				padding: 6.5px !important;
			}

			.on_image_text {
				font-size: 13px !important;
			}

			.news_text p {
				font-size: 13px !important;
			}

			.spacer_with_line {
				margin: 13px 0 !important;
			}

			.vid_sec_ind,
			.fc_section {
				width: 100%;
				display: block;
			}

			.vsi_img {
				padding-bottom: 15px;
			}

			.vsi_txt p {
				font-size: 14px !important;
			}

			.footer_center {
				width: 80%;
				height: auto !important;
				margin: auto;
			}

			.footer_center>td {
				width: 100% !important;
				display: block;
			}

			.fcs_bb {
				border-bottom: 1px solid #fff;
				margin-bottom: 30px !important;
				padding-bottom: 30px;
			}

			.fcs_no_b {
				border: 0 !important;
			}
		}

		p[data-f-id="pbf"] {
			display: none;
		}

		hr {
			height: 1px !important;
			background: #CECECE;
			margin: 20px 0;
		}

		h1 {
			color: white;
			text-shadow: -1px -1px 0 #000, 1px -1px 0 #000, -1px 1px 0 #000, 1px 1px 0 #000;
		}
	</style>
</head>

<body style="margin: 0; padding: 0;padding: 0 20px;" bgcolor="{{data_get($campaign, 'template.color')}}">
	<table bgcolor="{{data_get($campaign, 'template.color')}}" border="0" cellspacing="0" width="100%">
		<tr>
			<td width="600">
				<table align="center" style="border-collapse: collapse;max-width: 600px;" cellpadding="0" cellspacing="0">
					<tr>
						<td colspan="2">
							<!-- <p class="date_text" style="margin: 0;background: #2B3176;border-radius:3px;display: block;padding: 6px 0;justify-content:center;align-items:center;font-size: 19px;direction: rtl;text-align: center;color: #fff;font-family: 'heb_font',arial, sans-serif;">{{isset($campaign->template) ?  $campaign->template : $campaign->subject}}</p> -->
						</td>
					</tr>
					@if($campaign->template)
					@if($campaign->template->admin)
					<?php eval('?>' . \Blade::compileString($campaign->template->header_html, compact('campaign'))) ?>
					@else
					<tr>
						<td colspan="2">
							<table style="width: 100%;border-collapse: collapse;">
								<tr>
									<td colspan="2">
										<img src="{{$campaign->template->header_picture}}" alt="Dinner Header" style="width:100%;">
									</td>
								</tr>
							</table>
						</td>
					</tr>
					<tr class="spacer">
						<td colspan="2" style="padding: 15px;"></td>
					</tr>
					@endif
					@endif

					<!-- @include('components.dinner-header') -->
					@foreach($components as $component)
					@include('components.' . $component['layout'], ['component' => $component])
					@endforeach

					@if($campaign->template)
					@if($campaign->template->admin)
					<?php eval('?>' . \Blade::compileString($campaign->template->footer_html, compact('campaign'))) ?>
					@else
					<tr>
						<td colspan="2">
							<table style="width: 100%;border-collapse: collapse;">
								<tr>
									<td colspan="2">
										<img src="{{$campaign->template->footer_picture}}" alt="Dinner footer" style="width:100%;">
									</td>
								</tr>
							</table>
						</td>
					</tr>
					<tr class="spacer">
						<td colspan="2" style="padding: 15px;"></td>
					</tr>
					@endif
					@endif

				</table>

			</td>
		</tr>
	</table>
</body>

</html>