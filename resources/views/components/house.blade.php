<tr>
    <td class="pb-15" style="padding: 0 0 30px;">
        <table width="100%" cellpadding="0" cellspacing="0">
            <tbody>
                <tr>
                    <td class="plr-10 pb-20 pt-20" bgcolor="#ffffff" style="background: #fff; border-radius: 13px; -webkit-box-shadow: 0px 3px 22px 5px #DBDBDB; box-shadow: 0px 3px 22px 5px #DBDBDB; padding: 25px;">
                        <table width="100%" cellpadding="0" cellspacing="0">

                            <tbody>

                                <!-- Status -->
                                @if($component['attributes']['status'])
                                <tr>
                                    <td class="pb-10" style="padding: 0 0 20px;">
                                        <table cellpadding="0" cellspacing="0">
                                            <tbody>
                                                <tr>
                                                    @if($component['attributes']['status'] == 'new')
                                                    <td bgcolor="#309606" align="center" style="font:700 11px/14px Arial, Helvetica, sans-serif; color:#fff; background: #309606; border-radius: 30px; padding: 7px 16px; text-transform: uppercase;">
                                                        NEW LISTING
                                                    </td>
                                                    @elseif($component['attributes']['status'] == 'contract')
                                                    <td bgcolor="#E69331" align="center" style="font:700 11px/14px Arial, Helvetica, sans-serif; color:#fff; background: #E69331; border-radius: 30px; padding: 7px 16px; text-transform: uppercase;">
                                                        UNDER CONTRACT
                                                    </td>
                                                    @elseif($component['attributes']['status'] == 'backOnMarket')
                                                    <td bgcolor="#069680" align="center" style="font:700 11px/14px Arial, Helvetica, sans-serif; color:#fff; background: #069680; border-radius: 30px; padding: 7px 16px; text-transform: uppercase;">
                                                        BACK ON MARKET
                                                    </td>
                                                    @elseif($component['attributes']['status'] == 'activeOnMarket')
                                                    <td bgcolor="#DD1C1C" align="center" style="font:700 11px/14px Arial, Helvetica, sans-serif; color:#fff; background: #DD1C1C; border-radius: 30px; padding: 7px 16px; text-transform: uppercase;">
                                                        ACTIVE ON MARKET
                                                    </td>
                                                    @elseif($component['attributes']['status'] == 'offMarket')
                                                    <td bgcolor="#9A9D98" align="center" style="font:700 11px/14px Arial, Helvetica, sans-serif; color:#fff; background: #9A9D98; border-radius: 30px; padding: 7px 16px; text-transform: uppercase;">
                                                        OFF MARKET
                                                    </td>
                                                    @elseif($component['attributes']['status'] == 'comingSoonToMarket')
                                                    <td bgcolor="#5C5C5C" align="center" style="font:700 11px/14px Arial, Helvetica, sans-serif; color:#fff; background: #5C5C5C; border-radius: 30px; padding: 7px 16px; text-transform: uppercase;">
                                                        COMING SOON TO MARKET
                                                    </td>
                                                    @elseif($component['attributes']['status'] == 'priceDrop')
                                                    <td bgcolor="#AF0000" align="center" style="font:700 11px/14px Arial, Helvetica, sans-serif; color:#fff; background: #AF0000; border-radius: 30px; padding: 7px 16px; text-transform: uppercase;">
                                                        PRICE DROP
                                                    </td>
                                                    @elseif($component['attributes']['status'] == 'currentlynotacceptinganymoreoffers')
                                                    <td bgcolor="#9A9D98" align="center" style="font:700 11px/14px Arial, Helvetica, sans-serif; color:#fff; background: #9A9D98; border-radius: 30px; padding: 7px 16px; text-transform: uppercase;">
                                                        CURRENTLY NOT ACCEPTING ANYMORE OFFERS
                                                    </td>
                                                    @endif
                                                </tr>
                                            </tbody>
                                        </table>
                                    </td>
                                </tr>
                                @endif
                                <!-- End Status -->

                                <tr>
                                    <td class="img-flex pb-20" style="padding: 0 0 25px;">
                                        <a target="blank" href="#"><img src="{{data_get($component['images'], '0.url')}}" style="vertical-align:top; height: auto; width: 550px; border-radius: 6px;" border="0" width="550" alt=""></a>
                                    </td>
                                </tr>

                                <tr>
                                    <td class="plr-5">
                                        <table width="100%" cellpadding="0" cellspacing="0">
                                            <tbody>
                                                <tr>
                                                    <td class="fs-20" style="font:25px/35px Arial, Helvetica, sans-serif; color:#000; padding: 0 0 20px;">
                                                        {{$component['attributes']['address']}} <br>
                                                        <b>${{number_format(!empty($component['attributes']['price']) ? $component['attributes']['price'] : 0 )}}</b>
                                                    </td>
                                                </tr>

                                                <tr>
                                                    <td style="font:14px/23px Arial, Helvetica, sans-serif; color:#000; padding: 0 0 20px;">
                                                        {!! $component['attributes']['points'] !!}
                                                    </td>
                                                </tr>

                                                <tr>
                                                    <td>
                                                        <table width="100%" cellpadding="0" cellspacing="0">
                                                            <tbody>
                                                                <tr>
                                                                    <td class="active" align="center" style="font:700 19px/22px Arial, Helvetica, sans-serif; color:#fff; mso-padding-alt:15px 20px; background: #16405C; border-radius: 3px;" bgcolor="#16405C">
                                                                        <a target="_blank" style="text-decoration:none; color:#fffffe; display:block; padding:15px 20px;" href="{{$component['attributes']['link']}}">More Details</a>
                                                                    </td>
                                                                </tr>
                                                            </tbody>
                                                        </table>
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </td>
                                </tr>

                            </tbody>
                        </table>
                    </td>
                </tr>
            </tbody>
        </table>
    </td>
</tr>