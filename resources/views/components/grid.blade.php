<tr>
	<td colspan="2">
		<table style="width: 100%;background: #fff;border-radius: 3px;box-shadow: 0 3px 6px rgba(0,0,0,.16);padding: 10px 0;">
			<tr>
				<td colspan="2" style="">
					<p class="text_head" style="margin: 25px auto 10px;max-width: 495px;color: #040505;font-size: 22px;font-weight: 600;text-align: center;direction: rtl;font-family: 'heb_font',arial, sans-serif;">{{optional($component['attributes'])['title']}}</p>
					<p class="text_sub_head" style="margin: 0 auto 20px;max-width: 495px;color: #040505;font-size: 16px;font-weight: 400;text-align: center;direction: rtl;font-family: 'heb_font',arial, sans-serif;">{{optional($component['attributes'])['sub_title']}}</p>
				</td>
			</tr>
			@foreach ($component['images'] as $image)
			@if(
			($loop->iteration % 3 == 1)
			or ($loop->count % 3 == 1 and $loop->count == $loop->iteration)
			or ($loop->count % 3 == 2 and ($loop->count == $loop->iteration or $loop->count -1 == $loop->iteration))
			)
			<tr>
				<td colspan="2" style="padding: 5px 15px;">
					<table style="border-collapse: collapse; width: 100%;position: relative;background: #444444;font-size: 0;">
						<tr>
							<td style="padding:0;">
								<img style="top: 0;left: 0;object-fit: contain;" src="{{$image['url']}}" width="100%">
								@if($image['title'])
								<p class="on_image_text" style="font-size: 16px;color: #fff;text-align: center;position: absolute;bottom: 0;width: 100%;text-shadow: -1px -1px 0 #000, 1px -1px 0 #000, -1px 1px 0 #000, 1px 1px 0 #000;font-family: 'heb_font',arial, sans-serif;margin: 0;padding: 10px 0;">{{$image['title']}}</p>
								@endif
							</td>
						</tr>
					</table>
				</td>
			</tr>
			@elseif($loop->iteration % 3 == 2)
			<tr>
				<td style="padding: 5px 5px 5px 15px;width: 50%;">
					<table style="border-collapse: collapse;width: 100%;position: relative;background: #444444;font-size: 0;">
						<tr>
							<td style="padding:0;">
								<img style="top: 0;left: 0;object-fit: contain;" src="{{$image['url']}}" width="100%">
								@if($image['title'])
								<p class="on_image_text" style="font-size: 16px;color: #fff;text-align: center;position: absolute;bottom: 0;width: 100%;text-shadow: -1px -1px 0 #000, 1px -1px 0 #000, -1px 1px 0 #000, 1px 1px 0 #000;font-family: 'heb_font',arial, sans-serif;margin: 0;padding: 10px 0;">{{$image['title']}}</p>
								@endif
							</td>
						</tr>
					</table>
				</td>
				@elseif($loop->iteration % 3 == 0)
				<td style="padding: 5px 15px 5px 5px;width: 50%;">
					<table style="border-collapse: collapse;width: 100%;position: relative;background: #444444;font-size: 0;">
						<tr>
							<td style="padding:0;">
								<img style="top: 0;left: 0;object-fit: contain;" src="{{$image['url']}}" width="100%">
								@if($image['title'])
								<p class="on_image_text" style="font-size: 16px;color: #fff;text-align: center;position: absolute;bottom: 0;width: 100%;text-shadow: -1px -1px 0 #000, 1px -1px 0 #000, -1px 1px 0 #000, 1px 1px 0 #000;font-family: 'heb_font',arial, sans-serif;margin: 0;padding: 10px 0;">{{$image['title']}}</p>
								@endif
							</td>
						</tr>
					</table>
				</td>
			</tr>
			@endif

			@endforeach
		</table>
	</td>
</tr>
<tr class="spacer">
	<td colspan="2" style="padding: 15px;"></td>
</tr>
