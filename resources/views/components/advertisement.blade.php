@php
$link = optional($component['attributes'])['link'];
if($link) $link = substr( $link, 0, 4 ) != "http" && substr( $link, 0, 6 ) != "mailto" ? '\/\/' . $link : $link;
@endphp
<tr>
	<td colspan="2">
		<table style="width: 100%;border-collapse: collapse;">
			<tr>
				<td colspan="2">

                        @foreach (data_get($component, 'images') as $image)
							@if($link)
								@if(data_get($image, 'url'))
								<a target="_blank" href="{{$link}}">
									<img src="{{data_get($image,'url')}}" style="width:100%;">
								</a>
								@else
								<p style="text-align: center; font-size: 40px; text-decoration: underline;">
									<a target="_blank" href="{{$link}}">
										{!!$component['attributes']['text']!!}
									</a>
								</p>
								@endif
							@else
							<div>
								<img src="{{data_get($image,'url')}}" style="width:100%;">
							</div>
							@endif
                        @endforeach
				</td>
			</tr>
			@if(optional($component['attributes'])['text'])
			<tr>
				<td colspan="2" style="text-align: center; background: #fff;padding: 11px;">
					<table width="100%">
						<tr>
							<td width="25%"></td>
							<td align="center" style="height: 50px;text-align: center;">
								<div>
									<!--[if mso]>
                                    <v:rect xmlns:v="urn:schemas-microsoft-com:vml" xmlns:w="urn:schemas-microsoft-com:office:word" href="{{$link}}" style="height:50px;v-text-anchor:middle;padding:0 15px;" stroke="f" fillcolor="#2B3176">
                                    <w:anchorlock/>
                                    <center>
                                    <![endif]-->
									<a href="{{$link}}" style="background-color:#2B3176;color:#ffffff;display:inline-block;font-family:'heb_font',arial, sans-serif;font-size:18px;font-weight:700;line-height:50px;text-align:center;text-decoration:none;padding:0 15px;-webkit-text-size-adjust:none;">{{optional($component['attributes'])['text']}}</a>
									<!--[if mso]>
                                    </center>
                                    </v:rect>
                                <![endif]-->
								</div>
							</td>
							<td width="25%"></td>
						</tr>
					</table>
				</td>
			</tr>
			@endif
		</table>
	</td>
</tr>
<tr class="spacer">
	<td colspan="2" style="padding: 15px;"></td>
</tr>
