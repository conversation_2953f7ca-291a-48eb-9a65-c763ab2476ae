@php
$link = optional($component['attributes'])['link'];
if($link) $link = substr( $link, 0, 4 ) != "http" ? '\/\/' . $link : $link;
@endphp
<tr>
    <td colspan="2">
        <table style="width: 100%;border-collapse: collapse;">
        @foreach ($component['images'] as $image)
            <tr>
                <td colspan="2" style="padding: 5px 15px;">
                    <table style="border-collapse: collapse; width: 100%;position: relative;background: grey;font-size: 0;">
                        <tr>
                            <td style="padding:0;">
                                <img width="100%" style="top: 0;left: 0;object-fit: contain;" src="{{$image['url']}}">
                                @if($image['title'])
                                <p class="on_image_text" style="font-size: 16px;color: #fff;text-align: center;positin: absolute;bottom: 0;width: 100%;text-shadow: -1px -1px 0 #000, 1px -1px 0 #000, -1px 1px 0 #000, 1px 1px 0 #000;font-family: 'heb_font',arial, sans-serif;margin: 0;padding: 10px 0;">{{$image['title']}}</p>
                                @endif
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            @endforeach
        </table>
    </td>
</tr>
<tr class="spacer">
    <td colspan="2" style="padding: 15px;"></td>
</tr>