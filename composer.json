{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"bashy/laravel-campaignmonitor": "^5.0", "codenco-dev/nova-grid-system": "^1.0", "ebess/advanced-nova-media-library": "3.7", "epartment/nova-dependency-container": "^1.2", "ericlagarda/nova-text-card": "^1.1", "fideloper/proxy": "^4.2", "froala/nova-froala-field": "^3.4", "fruitcake/laravel-cors": "^1.0", "gabrieliuga/laravel-nova-field-iframe": "^1.0", "guzzlehttp/guzzle": "^6.3", "laravel/framework": "^8.38", "laravel/nova": "^3.23", "laravel/tinker": "^2.6", "league/flysystem-aws-s3-v3": "^1.0", "metrixinfo/nova-iframe": "^1.0", "optimistdigital/nova-multiselect-field": "^1.9", "shaulhaf/advanced-nova-media-library-vapor": "dev-master", "shaulhaf/email-summary": "*", "shaulhaf/nova-ajax-select": "dev-master", "shaulhaf/nova-goodtable": "dev-master", "shaulhaf/parent-image": "dev-master", "spatie/laravel-medialibrary": "^9.5", "timothyasp/nova-color-field": "^1.0", "whitecube/nova-flexible-content": "^0.2.8"}, "require-dev": {"facade/ignition": "^2.0", "fzaninotto/faker": "^1.9.1", "mockery/mockery": "^1.3.1", "nunomaduro/collision": "^4.1", "phpunit/phpunit": "^8.5"}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true}, "repositories": [{"type": "composer", "url": "https://nova.laravel.com"}, {"type": "vcs", "url": "https://github.com/shaulhaf/flexible"}, {"type": "vcs", "url": "https://github.com/shaulhaf/parent-image"}, {"type": "vcs", "url": "https://github.com/shaulhaf/nova-ajax-select"}, {"type": "vcs", "url": "https://github.com/shaulhaf/nova-goodtable"}, {"type": "vcs", "url": "https://github.com/shaulhaf/advanced-nova-media-library-vapor"}, {"type": "path", "url": "./nova-components/EmailSummary"}], "extra": {"laravel": {"dont-discover": []}}, "autoload": {"psr-4": {"App\\": "app/"}, "files": ["app/Http/helpers.php"], "classmap": ["database/seeds", "database/factories"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "minimum-stability": "dev", "prefer-stable": true, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}}