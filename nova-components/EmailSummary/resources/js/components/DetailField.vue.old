<template>
<div v-if="field.value.length" style="text-align:center;">
    <b-input-group class="mt-3 mb-3" size="sm" style="width:50%;margin;auto;">
        <b-form-input v-model="keyword" placeholder="Search" type="text"></b-form-input>
        <b-input-group-text slot="append">
            <b-btn class="p-0" :disabled="!keyword" variant="link" size="sm" @click="keyword = ''"><i class="fa.fa-remove"></i></b-btn>
        </b-input-group-text>
    </b-input-group>
    <b-table :fields="fields" :items="items" :keyword="keyword" :per-page="perPage" id="table">
	</b-table>
		    <b-pagination
			v-model="currentPage"
			:total-rows="rows"
			:per-page="perPage"
			aria-controls="table"
			></b-pagination>
</div>
    <!-- <panel-item :field="field" /> -->
</template>

<script>

import 'bootstrap/dist/css/bootstrap.css'
import 'bootstrap-vue/dist/bootstrap-vue.css'

export default {
    props: ['resource', 'resourceName', 'resourceId', 'field'],
    mounted(){
        this.dataArray = JSON.parse(this.field.value);
    },
    data () {
		return {
			keyword: '',
			dataArray: [],
			perPage: 5,
			currentPage: 1,
			fields: [
				{key: 'name', label: 'Name', sortable: true},
				{key: 'email', label: 'Email', sortable: true},
				{key: 'date', label: 'Last Open', sortable: true},
				{key: 'count', label: 'Opens', sortable: true}
			]
		}
	},
    computed: {
		items () {
			return this.keyword
				? this.dataArray.filter(item =>  item.email.includes(this.keyword))
				: this.dataArray
		},
		rows() {
			return this.items.length
		}
	}
}
</script>

<style lang="scss" scoped>
#table {
	margin: 0 auto;
	width: 500px;
	
	.input-group-text {
		padding: 0 .5em 0 .5em;
		
		.fa {
			font-size: 12px;
		}
	}
}
</style>