
<template>
    <vue-good-table
        :columns="columns"
        :rows="users"
        :search-options="{
            enabled: true,
            trigger: '',
            skipDiacritics: true,
            placeholder: 'Name or Email'
        }"
        :sort-options="{
            enabled: true,
        }"
        :pagination-options="{
            enabled: true,
            mode: 'records',
            perPage: 5,
            position: 'bottom',
            perPageDropdown: [5,10,20,30,40,50],
            nextLabel: 'Next',
            prevLabel: 'Prev',
            rowsPerPageLabel: 'Rows per page',
        }"
      />
    </div>
</template>
<script>
import { VueGoodTable } from 'vue-good-table';
import 'vue-good-table/dist/vue-good-table.css'
export default {
    props: ['resource', 'resourceName', 'resourceId', 'field'],
    data: () => ({
        users: [],
        currentSort:'name',
        currentSortDir:'asc',
        search: '',
        searchSelection: '',
        pageSize: 5,
        currentPage: 1,
        columns: [
            { label: 'Name', field: 'name', },
            { label: 'Email', field: 'email', },
            { label: 'Opens', field: 'count', globalSearchDisabled: true, type:'number'},
            { label: 'Last Opened', field: 'date', sortable: false, globalSearchDisabled: true,},
        ],
    }),

    created () {
            this.users = JSON.parse(this.field.value);
    },
    components: {
        VueGoodTable,
    }
}
</script>