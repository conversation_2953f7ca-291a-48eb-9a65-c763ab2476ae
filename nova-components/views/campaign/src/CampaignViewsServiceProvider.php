<?php

namespace NovaCustomViews\CampaignViews;

use <PERSON>vel\Nova\Nova;
use <PERSON>vel\Nova\Events\ServingNova;
use Illuminate\Support\Str;
use Illuminate\Support\ServiceProvider;

class CampaignViewsServiceProvider extends ServiceProvider
{
    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot()
    {
        Nova::serving(function (ServingNova $event) {
            $this->provideToScript($this->attachCustomViews('campaign'));
            Nova::script('campaign-views', __DIR__.'/../dist/js/views.js');
            Nova::style('campaign-views', __DIR__.'/../dist/css/views.css');
        });
    }

    private function attachCustomViews($resource)
    {
        $resourceName = Str::plural(Str::snake($resource, '-'));
        return [$resourceName => json_decode('{"detail":{"route":"detail","component":"Detail","name":"campaign-detail-view"}}', true)];
    }

    private function provideToScript($variables)
    {
        if (isset(Nova::$jsonVariables['novaCustomViews'])) {
            $variables = array_merge_recursive(Nova::$jsonVariables['novaCustomViews'], $variables);
        }
        Nova::provideToScript(['novaCustomViews' => $variables]);
    }
}
