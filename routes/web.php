<?php

use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

// auth()->login(\App\User::first());
Route::get('/', function () {
    return view('welcome');
});
Route::get('/campaign/{campaign}', 'CampaignController@show');
Route::get('/download/{media}', 'DownloadController@show');
Route::get('test', 'TestController@index');

Route::post('vapor/signed-storage-url', 'SignedStorageUrlController@store')
    ->middleware(config('nova.middleware'));
