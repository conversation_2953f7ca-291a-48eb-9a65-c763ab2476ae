<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

Route::get('/field/{field}/values', function (Request $request, $field) {
    $values = \App\Field::firstWhere('name', $field)->values;
    
    return collect(explode(',', $values))
        ->map(function ($item) {
            return [
                'label' => trim($item),
                'value' => trim($item),
                'display' => trim($item),
            ];
        })->filter();
});
