{"private": true, "scripts": {"dev": "npm run development", "development": "cross-env NODE_ENV=development node_modules/webpack/bin/webpack.js --progress --hide-modules --config=node_modules/laravel-mix/setup/webpack.config.js", "watch": "npm run development -- --watch", "watch-poll": "npm run watch -- --watch-poll", "hot": "cross-env NODE_ENV=development node_modules/webpack-dev-server/bin/webpack-dev-server.js --inline --hot --disable-host-check --config=node_modules/laravel-mix/setup/webpack.config.js", "prod": "npm run production", "production": "cross-env NODE_ENV=production node_modules/webpack/bin/webpack.js --no-progress --hide-modules --config=node_modules/laravel-mix/setup/webpack.config.js", "build-campaign": "cd nova-components/views/campaign && npm run dev", "build-campaign-prod": "cd nova-components/views/campaign && npm run prod", "build-resource-name": "cd nova-components/views/resource-name && npm run dev", "build-resource-name-prod": "cd nova-components/views/resource-name && npm run prod", "build-email-summary": "cd nova-components/EmailSummary && npm run dev", "build-email-summary-prod": "cd nova-components/EmailSummary && npm run prod"}, "devDependencies": {"@babel/core": "^7.9.6", "@babel/plugin-transform-runtime": "^7.9.6", "@babel/preset-env": "^7.9.6", "axios": "^0.19", "babel-loader": "^8.1.0", "cross-env": "^7.0", "laravel-mix": "1.0", "lodash": "^4.17.13", "resolve-url-loader": "^3.1.0", "sass": "^1.15.2", "sass-loader": "^8.0.0"}, "dependencies": {"@babel/runtime": "^7.9.6", "laravel-nova": "^1.2.2"}}